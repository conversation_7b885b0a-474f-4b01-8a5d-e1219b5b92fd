import { queryCollectionOptions } from "@tanstack/query-db-collection";
import { createCollection } from "@tanstack/react-db";
import { queryClient } from "~/lib/queryClient";
import type { LikedJokeSelect } from "~/validation/types";

export const likedJokesCollection = createCollection(
	queryCollectionOptions({
		queryClient,
		queryKey: ["likedJokes"],
		queryFn: async () => {
			const response = await fetch("/api/liked-joke");

			const likedJokes: LikedJokeSelect[] = await response.json();

			return likedJokes || [];
		},
		getKey: (item) => item.id,
		onInsert: async ({ transaction }) => {
			const { modified: newLikedJoke } = transaction.mutations[0];

			return await fetch("/api/liked-joke", {
				method: "POST",
				body: JSON.stringify(newLikedJoke),
			});
		},
		onDelete: async ({ transaction }) => {
			const { modified: deleted<PERSON>ike<PERSON><PERSON>oke } = transaction.mutations[0];

			return await fetch("/api/liked-joke", {
				method: "DELETE",
				body: JSON.stringify(deletedLikedJoke),
			});
		},
	}),
);
