import { createServerFileRoute } from "@tanstack/react-start/server";
import { getWebRequest } from "@tanstack/react-start/server";
import { eq } from "drizzle-orm";
import { db } from "~/db/client";
import { liked } from "~/db/schema";
import { auth } from "~/lib/auth/auth";

export const ServerRoute = createServerFileRoute(
	"/api/joke/get-joke-by-user",
).methods({
	GET: async () => {
		try {
			// Get the request to access headers for authentication
			const request = getWebRequest();
			const session = await auth.api.getSession({ headers: request.headers });

			if (!session?.user?.id) {
				return new Response(JSON.stringify({ error: "Unauthorized" }), {
					status: 401,
					headers: { "Content-Type": "application/json" },
				});
			}

			const userId = session.user.id;
			const likedJokes = await db.select().from(liked).where(eq(liked.userId, userId));

			return new Response(JSON.stringify(likedJokes), {
				status: 200,
				headers: { "Content-Type": "application/json" },
			});
		} catch (error) {
			console.error("Failed to get liked jokes:", error);
			return new Response(JSON.stringify({ error: "Internal server error" }), {
				status: 500,
				headers: { "Content-Type": "application/json" },
			});
		}
	},
});
